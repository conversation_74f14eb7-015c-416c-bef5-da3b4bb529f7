<div class="card card-flush h-xl-100">
  <div class="card-body pt-5">
    <nz-page-header [nzGhost]="false">
      <nz-page-header-title>Add Stock</nz-page-header-title>
      <nz-page-header-subtitle>Add stock details here</nz-page-header-subtitle>
      <nz-page-header-extra>
        <!-- Stock Mode Toggle -->
        <div style="display: flex; align-items: center; gap: 12px;">
          <span style="font-weight: 500;">Stock Type:</span>
          <nz-radio-group [(ngModel)]="stockMode" (ngModelChange)="toggleStockMode($event)">
            <label nz-radio nzValue="purchase">Purchase Stock</label>
            <label nz-radio nzValue="return">Return Stock</label>
          </nz-radio-group>
          <nz-tag [nzColor]="isPurchaseMode() ? 'blue' : 'orange'">
            {{ isPurchaseMode() ? 'Purchase Mode' : 'Return Mode' }}
          </nz-tag>
        </div>
      </nz-page-header-extra>
    </nz-page-header>
    <nz-divider></nz-divider>

    <!-- Return Stock Form -->
    <div *ngIf="isReturnMode()">
      <nz-card nzType="inner" nzTitle="Return Stock Details">
        <div class="row gx-10 mb-5">
          <div class="col-lg-6">
            <label class="required form-label">Sale Order Number</label>
            <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
              <input type="text" nz-input placeholder="Enter Sale Order Number" [(ngModel)]="saleOrderNumber" />
            </nz-input-group>
            <ng-template #suffixIconButton>
              <button nz-button nzType="primary" nzSearch (click)="searchSaleOrderForReturn()">
                Search
              </button>
            </ng-template>
          </div>
        </div>

        <!-- Customer Information -->
        <div *ngIf="dispatchedItems.SaleOrderId > 0" class="customer-info-section">
          <nz-alert nzType="info" nzMessage="Sale Order Found"
            [nzDescription]="'Customer: ' + dispatchedItems.CustomerName + ' | Order: ' + dispatchedItems.SaleOrderNumber + ' | Dispatch Date: ' + (dispatchedItems.DispatchDate | date)"
            nzShowIcon style="margin-bottom: 16px;">
          </nz-alert>

          <!-- Return Details Form -->
          <div class="row gx-10 mb-5">
            <div class="col-lg-4">
              <label class="required form-label">Return Reason</label>
              <nz-select [(ngModel)]="returnStockModel.ReturnReason" style="width: 100%;"
                nzPlaceHolder="Select return reason">
                <nz-option nzValue="Defective Product" nzLabel="Defective Product"></nz-option>
                <nz-option nzValue="Wrong Product Delivered" nzLabel="Wrong Product Delivered"></nz-option>
                <nz-option nzValue="Customer Dissatisfaction" nzLabel="Customer Dissatisfaction"></nz-option>
                <nz-option nzValue="Damaged in Transit" nzLabel="Damaged in Transit"></nz-option>
                <nz-option nzValue="Quality Issues" nzLabel="Quality Issues"></nz-option>
                <nz-option nzValue="Other" nzLabel="Other"></nz-option>
              </nz-select>
            </div>
            <div class="col-lg-4">
              <label class="required form-label">Returned By</label>
              <input nz-input [(ngModel)]="returnStockModel.ReturnedBy" placeholder="Enter person name" />
            </div>
            <div class="col-lg-4">
              <label class="form-label">Return Date</label>
              <nz-date-picker [(ngModel)]="returnStockModel.ReturnDate" style="width: 100%;"></nz-date-picker>
            </div>
          </div>

          <!-- Action Buttons -->
          <div style="margin-top: 16px;">
            <button nz-button nzType="primary" (click)="saveReturnStock()">
              Create Return Stock
            </button>
            <button nz-button (click)="resetReturnForm()" style="margin-left: 8px;">
              Reset
            </button>
          </div>
        </div>
      </nz-card>
    </div>

    <!-- Purchase Stock Form -->
    <div *ngIf="isPurchaseMode()">
      <nz-card nzType="inner" nzTitle="Stock Details">
        <div class="row gx-10 mb-5">
          <div class="col-lg-2">
            <label class="form-label">GRN</label>
            <!-- <input type="text" [(ngModel)] = "NewStock.Invoice.InvoiceNumber" class="form-control mb-2" placeholder="Invoice Number" value="" /> -->
            <nz-select nzShowSearch #microwaveRef class="form-select mb-2" (ngModelChange)="onSelectedGrnChange($event)"
              nzSize="large" [(ngModel)]="NewStock.Invoice.Grn" nzAllowClear nzPlaceHolder="Choose">
              <nz-option *ngFor="let s of this.FilteredPurchaseOrderList;" [nzValue]="s.Grn"
                [nzLabel]="s.Grn"></nz-option>
              <!--<nz-option *ngFor="let s of this.FilteredInvoiceListWithoutPO;" [nzValue]="s.Grn" [nzLabel]="s.Grn"></nz-option>-->
            </nz-select>
          </div>
          <div class="col-lg-4">
            <div class="row gx-10 mb-5">
              <div class="col-lg-8">
                <label class="required form-label">Purchase Order</label>
                <nz-select nzShowSearch #microwaveRef class="form-select mb-2" nzSize="large"
                  [(ngModel)]="NewStock.Invoice.Poid" (ngModelChange)="onSelectedPOChange($event)" nzAllowClear
                  nzPlaceHolder="Choose">
                  <nz-option *ngFor="let p of this.FilteredPurchaseOrderList;" [nzValue]="p.Poid"
                    [nzLabel]="p.Ponumber"></nz-option>
                </nz-select>
              </div>
              <div class="col-lg-4">
                <br>
                <button class="btn btn-sm btn-light-success" (click)="GetProductfromPO()" [disabled]="IsPoGet">Get
                  Products from Purchase order</button>
              </div>
            </div>


          </div>
          <div class="col-lg-3">
            <label class="required form-label">Supplier Name</label>
            <nz-select nzShowSearch #microwaveRef class="form-select mb-2" nzSize="large"
              (ngModelChange)="onSelectedSupplierChange($event)" [(ngModel)]="NewStock.Invoice.SupplierId" nzAllowClear
              nzPlaceHolder="Choose">
              <nz-option *ngFor="let s of this.SupplierList;" [nzValue]="s.SupplierId"
                [nzLabel]="s.SupplierName"></nz-option>
            </nz-select>
          </div>
          <div class="col-lg-2" *ngIf="!IsKnittingStock">
            <label class="required form-label">Invoice No.</label>
            <!-- <input type="text" [(ngModel)] = "NewStock.Invoice.InvoiceNumber" class="form-control mb-2" placeholder="Invoice Number" value="" /> -->
            <nz-select nzShowSearch #microwaveRef class="form-select mb-2"
              (ngModelChange)="onSelectedInvoiceChange($event)" nzSize="large" [(ngModel)]="NewStock.Invoice.InvoiceId"
              nzAllowClear nzPlaceHolder="Choose">
              <nz-option *ngFor="let s of this.InvoiceList;" [nzValue]="s.InvoiceId"
                [nzLabel]="s.InvoiceNumber"></nz-option>
            </nz-select>
          </div>
          <div class="col-lg-1" *ngIf="!IsKnittingStock">
            <button nz-button *ngIf="this.isInvoiceEditEnabled" nzType="primary" [nzSize]="smallsize" nzShape="round"
              style="margin-top: 30px; height: 40px;" (click)="OpenEditInvoice()"><span nz-icon
                nzType="edit"></span></button>

          </div>


          <!--end::Col-->
        </div>
        <div class="row gx-10 mb-5">
          <div class="col-lg-2">
            <label class="required form-label">Stock Date </label>
            <nz-date-picker class="form-select mb-2" nzSize="large" [(ngModel)]="NewStock.StockDate"
              nzValue="large"></nz-date-picker>
          </div>
          <div class="col-lg-2">
            <label class=" form-label">Eway Bill</label>
            <input type="text" [(ngModel)]="NewStock.Invoice.EwayBill" class="form-control mb-2"
              placeholder="E-Way Bill Number" value="" />
          </div>
          <div class="col-lg-2">
            <label class=" form-label">Eway Bill Date </label>
            <nz-date-picker class="form-select mb-2" nzSize="large" [(ngModel)]="NewStock.Invoice.EwayBillDate"
              nzValue="large"></nz-date-picker>
          </div>
          <div class="col-lg-2">
            <label class="required form-label">Invoice Date</label>
            <nz-date-picker class="form-select mb-2" nzSize="large" [(ngModel)]="NewStock.Invoice.InvoiceDate"
              nzValue="large"></nz-date-picker>
          </div>
          <!-- <div class="col-lg-3">
          <label class="required form-label">Total PO Value w/o GST</label>
          <input type="text" class="form-control mb-2" [(ngModel)]="NewStock.Invoice.InvoiceTotalPrice" name="price[]"
            placeholder="0.00" value="0.00" data-kt-element="price" />
        </div> -->
          <div class="col-lg-4">
            <label class="form-label">Upload Invoice <b style="color: red;">(Only PDF/JPEG/PNG files are
                accepted)</b></label>
            <input class="form-select mb-2" [(ngModel)]="uploadinvoice" type="file" (change)="onChange($event)">
            <div *ngIf="uploadProgress.length > 0" class="mt-3">
              <div *ngFor="let progress of uploadProgress" class="mb-2">
                <span>{{progress.filename}}</span>
                <nz-progress [nzPercent]="progress.progress" [nzStatus]="progress.status" [nzShowInfo]="true">
                </nz-progress>
              </div>
            </div>
            <!-- <nz-upload
          nzName="file"
          [(nzFileList)]="files"
          [nzTransformFile]="transformFile"
          [nzData]="getExtraData"
          [nzFileType]="'image/png,image/jpeg'"
          [nzAction]="mockOSSData.host"
          (nzChange)="onChange($event)"
          [nzLimit]="1"
        >

          <button nz-button class="form-control mb-2">
            <i nz-icon nzType="upload"></i>
            Upload Invoice
          </button>
        </nz-upload> -->
            <!-- <input type="text" name="sku" [(ngModel)] = "NewStock.SupplierId" class="form-control mb-2" placeholder="Upload Invoice" value="" /> -->
          </div>
        </div>
      </nz-card>
      <nz-card nzType="inner" style="margin-top:16px;" nzTitle="Products" [nzExtra]="extraTemplate">


        <table class="table align-middle table-row-dashed" id="kt_ecommerce_products_table"
          *ngIf="this.NewStock.StockProduct.length>0">
          <!--begin::Table head-->
          <thead class="ant-table-thead ng-star-inserted">
            <!--begin::Table row-->
            <tr class="ant-table-row ng-star-inserted">
              <th class="ant-table-cell">Product Type</th>
              <th>Product Name</th>
              <!-- <th>SKU</th> -->
              <!-- <th>Barcode</th> -->
              <th>Mfd Date</th>
              <th>Expiry</th>
              <th>Unit</th>
              <th>Quantity</th>
              <th>Inv./Unit</th>
              <th>S&H/Unit</th>
              <th>Frt & Ins./Unit</th>
              <th>Misc/Unit</th>
              <th>Total Price/Unit</th>
              <th>Grade</th>
              <th *ngIf="SelectedProductType == 'Finished'">Thick</th>
              <th *ngIf="SelectedProductType == 'Finished'">Grain</th>
              <th *ngIf="SelectedProductType == 'Finished'">Width</th>
              <th *ngIf="SelectedProductType == 'Finished'">Color</th>
              <th *ngIf="SelectedProductType == 'Finished'">Post Process</th>
              <th>Actions</th>
            </tr>
            <!--end::Table row-->
          </thead>
          <!--end::Table head-->
          <!--begin::Table body-->
          <tbody class="fw-bold text-gray-600">
            <!--begin::Table row-->
            <tr *ngFor="let spl of this.NewStock.StockProduct;">
              <td>
                {{spl.ProductType}}
              </td>
              <td>
                {{spl.ProductName}}
              </td>
              <!-- <td>
              {{spl.Sku}}
            </td>
            <td>
              {{spl.Barcode}}
            </td> -->
              <td>
                {{spl.ManufacturedDate | date: 'dd-MMM-yyyy'}}
              </td>
              <td>
                {{spl.ExpiryDate | date: 'dd-MMM-yyyy'}}
              </td>
              <td>
                {{spl.Unit}}
              </td>
              <td>
                {{spl.AcceptedQuantity}}
              </td>
              <td>
                &#8377; {{spl.InvoicePricePerUnit}}
              </td>
              <td>
                &#8377; {{spl.ShippingHandlingPerUnit}}
              </td>
              <td>
                &#8377; {{spl.FreightPerUnit}}
              </td>
              <td>
                &#8377; {{spl.MiscPerUnit}}
              </td>
              <td>
                &#8377; {{spl.PricePerUnit}}
              </td>
              <td>
                {{spl.Grade}}
              </td>
              <td *ngIf="SelectedProductType == 'Finished'">
                {{spl.ThicknessNumber}}
              </td>
              <td *ngIf="SelectedProductType == 'Finished'">
                {{spl.GrainName}}
              </td>
              <td *ngIf="SelectedProductType == 'Finished'">
                {{spl.WidthNumber}}
              </td>
              <td *ngIf="SelectedProductType == 'Finished'">
                {{spl.ColorName}}
              </td>
              <td *ngIf="SelectedProductType == 'Finished'">
                {{spl.PostProcess}}
              </td>

              <td>
                <a class="btn btn-sm btn-light-primary" (click)="EditProduct(spl)">Add Details</a>
                <a class="btn btn-sm btn-light-danger" (click)="RemoveStockProduct(spl)">Remove</a>
              </td>
            </tr>
          </tbody>
        </table>
        <table class="producttotaltable" style="width: 100%;">

          <tbody>
            <tr>
              <td style="width:60%">

              </td>
              <td>
                <table class="producttotaltable" style="text-align: right;width: 100%;">
                  <tbody>
                    <tr>
                      <td style="width:50%"><b> Sub Total (A) </b></td>
                      <td style="width:50%"> &#8377; {{this.calculatesubtotal()}}</td>
                    </tr>
                    <tr>
                      <td style="width:50%"><b>Total IGST/TAX (B) </b></td>
                      <td style="width:50%">
                        <nz-input-group nzPrefix="&#8377;">
                          <input nz-input type="number" class="form-control" (change)="this.CalculateGTotal();"
                            [(ngModel)]="NewStock.Invoice.GST" nzSize="small"
                            style="width: 70%;float: right;padding: 5px;height: 28px;text-align: right;border: 0;border-bottom: 1px solid;border-radius: 0;" />
                        </nz-input-group>
                      </td>
                    </tr>
                    <tr>
                      <td style="width:50%"><b>Shipping & Handling (C) </b> <i nz-icon nzType="info-circle" nz-tooltip
                          nzTooltipTitle="Total Shipping and Handling Charges"></i></td>
                      <td style="width:50%">
                        <nz-input-group nzPrefix="&#8377;">
                          <input nz-input type="number" (change)="this.CalculateTotalShippingHandling();"
                            [(ngModel)]="NewStock.Invoice.ShippingHandling" class="form-control" nzSize="small"
                            style="width: 70%;float: right;padding: 5px;height: 28px;text-align: right;border: 0;border-bottom: 1px solid;border-radius: 0;" />
                        </nz-input-group>
                      </td>
                    </tr>
                    <tr>
                      <td style="width:50%"><b>Freight & Insurance (D) </b> <i nz-icon nzType="info-circle" nz-tooltip
                          nzTooltipTitle="Total Freight and Insurance Charges"></i></td>
                      <td style="width:50%">
                        <nz-input-group nzPrefix="&#8377;">
                          <input nz-input type="number" (change)="this.CalculateTotalFreight();"
                            [(ngModel)]="NewStock.Invoice.FreightInsurance" class="form-control" nzSize="small"
                            style="width: 70%;float: right;padding: 5px;height: 28px;text-align: right;border: 0;border-bottom: 1px solid;border-radius: 0;" />
                        </nz-input-group>
                      </td>
                    </tr>
                    <tr>
                      <td style="width:50%"> <b>Other/Misc (E)</b> <i nz-icon nzType="info-circle" nz-tooltip
                          nzTooltipTitle="Total Miscellaneous Charges"></i>
                      </td>
                      <td style="width:50%">
                        <nz-input-group nzPrefix="&#8377;">
                          <input nz-input type="number" (change)="this.CalculateTotalMisc();"
                            [(ngModel)]="NewStock.Invoice.OtherCharges" class="form-control" nzSize="small"
                            style="width: 70%;float: right;padding: 5px;height: 28px;text-align: right;border: 0;border-bottom: 1px solid;border-radius: 0;" />
                        </nz-input-group>
                      </td>
                    </tr>
                  </tbody>

                  <tfoot>
                    <tr style="border-top: 2px solid;">
                      <td style="width:50%"><b>Invoice Total (A+B+C+D+E)</b><i nz-icon nzType="info-circle" nz-tooltip
                          nzTooltipTitle="Total Invoice Amount (B+C+D+E)"></i>
                      </td>
                      <td style="width:50%"> &#8377; {{ NewStock.Invoice.InvoiceTotal }}</td>
                    </tr>
                  </tfoot>
                  <tfoot>
                    <tr style="border-top: 2px solid;">
                      <td style="width:50%"><b>Total without GST (A+C+D+E)</b><i nz-icon nzType="info-circle" nz-tooltip
                          nzTooltipTitle="Total PO Value w/o GST"></i>
                      </td>
                      <td style="width:50%"> &#8377; {{ NewStock.Invoice.InvoiceTotalPrice }}</td>
                    </tr>
                  </tfoot>
                </table>
              </td>
            </tr>
          </tbody>
        </table>
      </nz-card>
      <ng-template #extraTemplate>
        <!-- <a class="btn btn-sm btn-success" (click)="showModal()">Add Product</a> -->
      </ng-template>
      <br>
      <div class="d-flex justify-content-end">
        <label style="margin:10px" nz-checkbox [(ngModel)]="NewStock.Invoice.IsPocomplete"><b> Is PO Completed
          </b></label>
        <button type="submit" id="kt_ecommerce_add_product_submit" [disabled]="isLoading" (click)="SaveStock()"
          class="btn btn-primary">
          <i nz-icon nzType="loading" *ngIf="isLoading"></i>
          <span class="indicator-label">Save Changes</span>
        </button>
      </div>
    </div>
  </div>



  <nz-modal [nzWidth]="1000" [nzStyle]="{ top: '20px' }" [(nzVisible)]="isVisible" [nzTitle]="modalTitle"
    [nzContent]="modalContent" [nzFooter]=null (nzOnCancel)="handleCancel()">
    <ng-template #modalTitle>Add Product</ng-template>

    <ng-template #modalContent>
      <div class="container-fluid add-stock-modal">
        <!-- Product Information Display Section -->
        <div class="mb-5 fv-row" *ngIf="selectedProduct > 0">
          <div class="alert alert-info mb-4">
            <h6 class="mb-3"><strong>Product Information</strong></h6>
            <div class="row g-3 mb-2">
              <div class="col-lg-3 col-md-6">
                <div class="info-item">
                  <strong>Product Type:</strong><br>
                  <span class="text-muted">{{SelectedProductType}}</span>
                </div>
              </div>
              <div class="col-lg-3 col-md-6">
                <div class="info-item">
                  <strong>Category:</strong><br>
                  <span class="text-muted">{{getSelectedCategoryName()}}</span>
                </div>
              </div>
              <div class="col-lg-3 col-md-6">
                <div class="info-item">
                  <strong>SubCategory:</strong><br>
                  <span class="text-muted">{{getSelectedFirstSubCategoryName()}}</span>
                </div>
              </div>
              <div class="col-lg-3 col-md-6">
                <div class="info-item">
                  <strong>2nd SubCategory:</strong><br>
                  <span class="text-muted">{{getSelectedSecondSubCategoryName()}}</span>
                </div>
              </div>
            </div>
            <div class="row g-3">
              <div class="col-lg-4 col-md-6">
                <div class="info-item">
                  <strong>Product Name:</strong><br>
                  <span class="text-muted">{{getSelectedProductName()}}</span>
                </div>
              </div>
              <div class="col-lg-4 col-md-6">
                <div class="info-item">
                  <strong>Stock Date:</strong><br>
                  <span class="text-muted">{{formatStockDate()}}</span>
                </div>
              </div>
              <div class="col-lg-4 col-md-6">
                <div class="info-item">
                  <strong>Supplier:</strong><br>
                  <span class="text-muted">{{getSelectedSupplierName()}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- Supplier Product Name Section -->
        <div class="mb-5 fv-row">
          <h6 class="mb-3"><strong>Supplier Product Name</strong></h6>
          <div class="row g-3 align-items-start">
            <!-- Supplier Product Name Field -->
            <div class="col-lg-6">
              <label class="required form-label mb-2">Supplier Product Name (As per Invoice)</label>

              <!-- Dropdown when existing mappings exist for the selected product -->
              <nz-select nzShowSearch class="form-select w-100" nzSize="large" [(ngModel)]="selectedSupplierProductName"
                (ngModelChange)="onSupplierProductNameChange($event)" nzAllowClear
                nzPlaceHolder="Select supplier product name"
                *ngIf="!showSupplierProductInput && getAvailableSupplierProductNames().length > 0">
                <nz-option *ngFor="let mapping of getAvailableSupplierProductNames()"
                  [nzValue]="mapping.SupplierProductName" [nzLabel]="mapping.SupplierProductName">
                  <div class="supplier-option-content">
                    <div><strong>{{mapping.SupplierProductName}}</strong></div>
                    <div class="supplier-info">
                      <nz-tag *ngIf="mapping.IsFrequentlyUsed" nzColor="blue" nzSize="small">Frequently Used</nz-tag>
                      <small class="text-muted"> (Used {{mapping.UsageCount}} times)</small>
                    </div>
                  </div>
                </nz-option>
                <nz-option [nzValue]="'__ADD_NEW__'" [nzLabel]="'+ Add new supplier product name'">
                  <div class="add-new-option">
                    <i nz-icon nzType="plus" style="color: #1890ff; margin-right: 4px;"></i> Add new supplier product
                    name
                  </div>
                </nz-option>
              </nz-select>

              <!-- Input field when no mappings exist or user wants to add new -->
              <input nz-input type="text" [(ngModel)]="newSupplierProductName"
                (ngModelChange)="onNewSupplierProductNameChange()" placeholder="Enter supplier's product name"
                nzSize="large" class="w-100"
                *ngIf="showSupplierProductInput || getAvailableSupplierProductNames().length === 0" />
            </div>

            <!-- Display Selected Product and Supplier Information -->
            <div class="col-lg-6" *ngIf="selectedSupplierProductName && selectedSupplierProductName !== '__ADD_NEW__'">
              <label class="form-label mb-2 text-muted">Selected Mapping</label>
              <div class="alert alert-success mb-0">
                <div class="mb-2"><strong>Supplier Product Name:</strong> {{selectedSupplierProductName}}</div>
                <div class="mb-2"><strong>Product Name:</strong> {{getSelectedProductName()}}</div>
                <div><strong>Supplier:</strong> {{getSelectedSupplierName()}}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Date Information Section -->
        <div class="mb-5 fv-row">
          <h6 class="mb-3"><strong>Date Information</strong></h6>
          <div class="row g-3">
            <div class="col-lg-6 col-md-6">
              <label class="form-label mb-2">Manufactured Date</label>
              <nz-date-picker class="form-select w-100" nzSize="large" [nzDisabledDate]="disabledStartDate"
                [(ngModel)]="NewStockProduct.ManufacturedDate" nzValue="default"></nz-date-picker>
            </div>
            <div class="col-lg-6 col-md-6">
              <label class="form-label mb-2">Expiry Date</label>
              <nz-date-picker class="form-select w-100" nzSize="large" [nzDisabledDate]="disabledEndDate"
                [(ngModel)]="NewStockProduct.ExpiryDate" nzValue="default"></nz-date-picker>
            </div>
          </div>
        </div>
        <!-- Quantity and Pricing Section -->
        <div class="mb-5 fv-row">
          <h6 class="mb-3"><strong>Quantity and Pricing Details</strong></h6>

          <!-- First Row: Basic Quantity Info -->
          <div class="row g-3 mb-3">
            <div class="col-lg-4 col-md-6">
              <label class="required form-label mb-2">Measure Unit</label>
              <nz-select nzShowSearch class="form-select w-100" nzSize="large" [(ngModel)]="NewStockProduct.Unit"
                disabled="true" nzAllowClear nzPlaceHolder="Choose">
                <nz-option *ngFor="let s of this.MeasureUnits;" [nzValue]="s.Unit" [nzLabel]="s.Unit"></nz-option>
              </nz-select>
            </div>
            <div class="col-lg-4 col-md-6">
              <label class="required form-label mb-2">Quantity</label>
              <input type="number" name="Quantity" [(ngModel)]="NewStockProduct.Quantity" class="form-control w-100"
                disabled="true" placeholder="Quantity" value="" nzSize="large" />
            </div>
            <div class="col-lg-4 col-md-6">
              <label class="required form-label mb-2">Received Quantity</label>
              <input type="number" name="AcceptedQuantity" [(ngModel)]="NewStockProduct.AcceptedQuantity"
                class="form-control w-100" placeholder="Accepted Quantity" value="" nzSize="large" />
            </div>
          </div>

          <!-- Second Row: Pricing Details -->
          <div class="row g-3 mb-3">
            <div class="col-lg-3 col-md-6">
              <label class="form-label mb-2">S&H/Unit (A)
                <i nz-icon nzType="info-circle" nz-tooltip nzTooltipTitle="Shipping and Handling Charges per unit"></i>
              </label>
              <nz-input-group nzPrefix="&#8377;" class="w-100">
                <input nz-input type="number" class="form-control" name="shippingHandling"
                  [(ngModel)]="NewStockProduct.ShippingHandlingPerUnit" nzSize="large" placeholder="0.00" value="0.00"
                  data-kt-element="price" (change)="calculateTotalPrice()" />
              </nz-input-group>
            </div>
            <div class="col-lg-3 col-md-6">
              <label class="form-label mb-2">Frt & Ins./Unit (B)
                <i nz-icon nzType="info-circle" nz-tooltip nzTooltipTitle="Freight and Insurance Charges per unit"></i>
              </label>
              <nz-input-group nzPrefix="&#8377;" class="w-100">
                <input nz-input type="number" class="form-control" name="freight"
                  [(ngModel)]="NewStockProduct.FreightPerUnit" nzSize="large" placeholder="0.00" value="0.00"
                  data-kt-element="price" (change)="calculateTotalPrice()" />
              </nz-input-group>
            </div>
            <div class="col-lg-3 col-md-6">
              <label class="form-label mb-2">Misc/Unit (C)</label>
              <nz-input-group nzPrefix="&#8377;" class="w-100">
                <input nz-input type="number" class="form-control" name="misc" [(ngModel)]="NewStockProduct.MiscPerUnit"
                  nzSize="large" placeholder="0.00" value="0.00" data-kt-element="price"
                  (change)="calculateTotalPrice()" />
              </nz-input-group>
            </div>
            <div class="col-lg-3 col-md-6">
              <label class="required form-label mb-2">Invoice Price/Unit (D)
                <i nz-icon nzType="info-circle" nz-tooltip nzTooltipTitle="Per Unit Price (as per invoice)"></i>
              </label>
              <nz-input-group nzPrefix="&#8377;" class="w-100">
                <input nz-input type="number" class="form-control" name="invoicePrice"
                  [(ngModel)]="NewStockProduct.InvoicePricePerUnit" nzSize="large" placeholder="0.00" value="0.00"
                  data-kt-element="price" (change)="calculateTotalPrice()" />
              </nz-input-group>
            </div>
          </div>

          <!-- Third Row: Total and Grade -->
          <div class="row g-3">
            <div class="col-lg-6 col-md-6">
              <label class="form-label mb-2">Total Price/Unit
                <i nz-icon nzType="info-circle" nz-tooltip nzTooltipTitle="A+B+C+D"></i>
              </label>
              <div class="form-control-plaintext bg-light p-3 rounded">
                <strong>&#8377; {{NewStockProduct.PricePerUnit | number:'1.2-2'}}</strong>
              </div>
            </div>
            <div class="col-lg-6 col-md-6">
              <label class="form-label mb-2">Grade</label>
              <nz-select class="form-select w-100" nzSize="large" [(ngModel)]="NewStockProduct.Grade" nzAllowClear
                nzPlaceHolder="Choose Grade">
                <nz-option nzValue="NA" nzLabel="NA"></nz-option>
                <nz-option nzValue="0" nzLabel="0"></nz-option>
                <nz-option nzValue="1" nzLabel="1"></nz-option>
                <nz-option nzValue="2" nzLabel="2"></nz-option>
                <nz-option nzValue="3" nzLabel="3"></nz-option>
                <nz-option nzValue="4" nzLabel="4"></nz-option>
                <nz-option nzValue="5" nzLabel="5"></nz-option>
                <nz-option nzValue="6" nzLabel="6"></nz-option>
                <nz-option nzValue="7" nzLabel="7"></nz-option>
                <nz-option nzValue="8" nzLabel="8"></nz-option>
                <nz-option nzValue="9" nzLabel="9"></nz-option>
                <nz-option nzValue="A" nzLabel="A"></nz-option>
                <nz-option nzValue="B" nzLabel="B"></nz-option>
                <nz-option nzValue="C" nzLabel="C"></nz-option>
                <nz-option nzValue="D" nzLabel="D"></nz-option>
                <nz-option nzValue="E" nzLabel="E"></nz-option>
              </nz-select>
            </div>
          </div>
        </div>


        <!-- Finished Product Specifications -->
        <div class="mb-5 fv-row" *ngIf="SelectedProductType == 'Finished'">
          <h6 class="mb-3"><strong>Finished Product Specifications</strong></h6>
          <div class="row g-3">
            <div class="col-lg-3 col-md-6">
              <label class="required form-label mb-2">Thickness</label>
              <nz-select class="form-select w-100" nzShowSearch [(ngModel)]="NewStockProduct.ThicknessId" nzSize="large"
                nzPlaceHolder="Choose" [ngModelOptions]="{standalone: true}">
                <nz-option *ngFor="let s of this.ThicknessList;" [nzValue]="s.ThicknessId"
                  [nzLabel]="s.ThicknessNumber"></nz-option>
              </nz-select>
            </div>
            <div class="col-lg-3 col-md-6">
              <label class="required form-label mb-2">Grain</label>
              <nz-select class="form-select w-100" nzShowSearch [(ngModel)]="NewStockProduct.GrainId" nzSize="large"
                nzAllowClear nzPlaceHolder="Choose" [ngModelOptions]="{standalone: true}">
                <nz-option *ngFor="let s of this.GrainList;" [nzValue]="s.GrainId" [nzLabel]="s.GrainName"></nz-option>
              </nz-select>
            </div>
            <div class="col-lg-3 col-md-6">
              <label class="required form-label mb-2">Width</label>
              <nz-select class="form-select w-100" nzShowSearch [(ngModel)]="NewStockProduct.WidthId" nzSize="large"
                nzAllowClear nzPlaceHolder="Choose" [ngModelOptions]="{standalone: true}">
                <nz-option *ngFor="let s of this.WidthList;" [nzValue]="s.WidthId"
                  [nzLabel]="s.WidthNumber"></nz-option>
              </nz-select>
            </div>
            <div class="col-lg-3 col-md-6">
              <label class="required form-label mb-2">Color</label>
              <nz-select class="form-select w-100" nzShowSearch [(ngModel)]="NewStockProduct.ColorId" nzSize="large"
                nzAllowClear nzPlaceHolder="Choose" [ngModelOptions]="{standalone: true}">
                <nz-option *ngFor="let s of this.ColorList;" [nzValue]="s.ColorId" [nzLabel]="s.ColorName"></nz-option>
              </nz-select>
            </div>
          </div>
          <div class="row g-3 mt-2">
            <div class="col-lg-12">
              <label class="required form-label mb-2">Post Process</label>
              <input type="text" class="form-control w-100" name="postProcess" [(ngModel)]="NewStockProduct.PostProcess"
                nzSize="large" placeholder="Enter Post Process" />
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="mb-4 fv-row">
          <div class="d-flex justify-content-end">
            <button type="button" class="btn btn-secondary me-3" (click)="handleCancel()">
              Cancel
            </button>
            <button type="submit" id="kt_ecommerce_add_product_submit" (click)="AddNewProductRecord()"
              class="btn btn-success" [disabled]="isLoading">
              <span class="indicator-label" *ngIf="!isLoading">
                <i nz-icon nzType="check" class="me-2"></i>Update
              </span>
              <span class="indicator-progress" *ngIf="isLoading">
                <span class="spinner-border spinner-border-sm align-middle me-2"></span>
                Please wait...
              </span>
            </button>
          </div>
        </div>
      </div>
      <!--end::Modal Content-->
    </ng-template>



  </nz-modal>
  <nz-modal [nzWidth]="300" [nzStyle]="{ top: '20px' }" [(nzVisible)]="isVisibleEdit" [nzTitle]="modalTitleEdit"
    [nzContent]="modalContentEdit" [nzFooter]=null (nzOnCancel)="handleCancelEdit()">
    <ng-template #modalTitleEdit>Edit Invoice Number</ng-template>

    <ng-template #modalContentEdit>
      <div>
        <div class="mb-10 fv-row">
          <div class="row gx-10 mb-5">
            <div class="col-lg-12">
              <label class=" form-label">Select Invoice </label><br>
              <nz-select nzShowSearch class="form-select mb-2" (ngModelChange)="UpdateInvoiceNo($event)" nzSize="large"
                [(ngModel)]="UpdatedInvoiceNo.NewInvoiceId" nzAllowClear nzPlaceHolder="Choose">
                <nz-option *ngFor="let s of this.InvoiceListWithoutPO;" [nzValue]="s.InvoiceId"
                  [nzLabel]="s.InvoiceNumber"></nz-option>
              </nz-select>
            </div>
          </div>
          <div class="row gx-10 mb-5">
            <div class="col-lg-12">
              <label class=" form-label">Invoice Number </label><br>
              <input type="text" name="InvoiceId" [(ngModel)]="UpdatedInvoiceNo.NewInvoiceNo" class="form-control"
                placeholder="Invoice Number" value="" nzSize="large" />
            </div>
          </div>
          <br>
          <div class="row gx-10 mb-5">
            <div class="col-lg-12">
              <label class=" form-label">Purchase Order </label><br>
              <nz-select nzShowSearch #microwaveRef class="form-select mb-2" nzSize="large"
                [(ngModel)]="UpdatedInvoiceNo.NewPO" nzPlaceHolder="Choose">
                <nz-option *ngFor="let p of this.FilteredPurchaseOrderList;" [nzValue]="p.Poid"
                  [nzLabel]="p.Ponumber"></nz-option>
              </nz-select>
            </div>
          </div>
          <br>
          <div class="d-flex justify-content-end">
            <button type="submit" id="kt_ecommerce_add_product_submit" [disabled]="isLoading" (click)="UpdateInvoice()"
              class="btn btn-primary">
              <i nz-icon nzType="loading" *ngIf="isLoading"></i>
              <span class="indicator-label">Save Changes</span>
            </button>
          </div>
        </div>
      </div>
    </ng-template>
  </nz-modal>

  <!-- Close Purchase Stock Form -->
</div>