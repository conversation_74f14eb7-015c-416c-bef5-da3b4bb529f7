import { Component, OnInit } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { environment } from 'src/environments/environment';
import { AlertMessageService } from 'src/PmsUIApp/Services/AlertMessageService';
import {
  ReturnStockModel,
  ReturnStockItemModel,
  DispatchedItemsForReturnModel,
  DispatchedItemDetailModel,
  ReturnStockListModel,
  ReturnStockFilterModel
} from 'src/PmsUIApp/Models/StockModel';
import { CustomerModel } from 'src/PmsUIApp/Models/SupplierModel';

@Component({
  selector: 'app-return-stock',
  templateUrl: './return-stock.component.html',
  styleUrls: ['./return-stock.component.css']
})
export class ReturnStockComponent implements OnInit {
  ApiUrl = environment.Api_Url;

  // Models
  returnStockModel: ReturnStockModel = new ReturnStockModel();
  dispatchedItems: DispatchedItemsForReturnModel = new DispatchedItemsForReturnModel();
  returnStockList: ReturnStockListModel[] = [];
  filterModel: ReturnStockFilterModel = new ReturnStockFilterModel();

  // UI State
  currentStep: number = 1; // 1: Search Order, 2: Select Items, 3: Return Details, 4: Confirmation
  isLoading: boolean = false;
  isTableLoading: boolean = false;
  showReturnForm: boolean = false;

  // Data Lists
  customerList: CustomerModel[] = [];
  saleOrderNumber: string = '';

  // Return Conditions
  returnConditions = [
    { value: 'Good', label: 'Good Condition' },
    { value: 'Damaged', label: 'Damaged' },
    { value: 'Defective', label: 'Defective' }
  ];

  constructor(
    private http: HttpClient,
    private router: Router,
    private alertService: AlertMessageService
  ) { }

  ngOnInit(): void {
    this.loadCustomers();
    this.loadReturnStockList();
  }

  loadCustomers() {
    let url = this.ApiUrl + 'customer/GetAllCustomers';
    this.http.get<CustomerModel[]>(url).subscribe({
      next: (res) => {
        this.customerList = res;
      },
      error: (res) => {
        this.alertService.error('Error loading customers');
      }
    });
  }

  loadReturnStockList() {
    this.isTableLoading = true;
    let url = this.ApiUrl + 'stock/GetReturnStockList';
    this.http.post<ReturnStockListModel[]>(url, this.filterModel).subscribe({
      next: (res) => {
        this.returnStockList = res;
        this.isTableLoading = false;
      },
      error: (res) => {
        this.alertService.error('Error loading return stock list');
        this.isTableLoading = false;
      }
    });
  }

  searchSaleOrder() {
    if (!this.saleOrderNumber) {
      this.alertService.error('Please enter Sale Order Number');
      return;
    }

    this.isLoading = true;
    let url = this.ApiUrl + `stock/GetDispatchedItemsForReturn?saleOrderNumber=${this.saleOrderNumber}`;
    this.http.get<DispatchedItemsForReturnModel>(url).subscribe({
      next: (res) => {
        this.dispatchedItems = res;
        this.returnStockModel.OriginalSaleOrderId = res.SaleOrderId;
        this.returnStockModel.CustomerId = res.CustomerId;
        this.returnStockModel.CustomerName = res.CustomerName;
        this.returnStockModel.SaleOrderNumber = res.SaleOrderNumber;
        this.returnStockModel.OriginalDispatchDate = res.DispatchDate;
        this.currentStep = 2;
        this.isLoading = false;
      },
      error: (res) => {
        this.alertService.error('Sale Order not found or no dispatched items available');
        this.isLoading = false;
      }
    });
  }

  selectItemForReturn(item: DispatchedItemDetailModel) {
    const returnItem = new ReturnStockItemModel();
    returnItem.ProductId = item.ProductId;
    returnItem.ProductName = item.ProductName;
    returnItem.Unit = item.Unit;
    returnItem.OriginalDispatchedQuantity = item.DispatchedQuantity;
    returnItem.OriginalManufacturedDate = item.ManufacturedDate;
    returnItem.ThicknessId = item.ThicknessId;
    returnItem.GrainId = item.GrainId;
    returnItem.WidthId = item.WidthId;
    returnItem.ColorId = item.ColorId;
    returnItem.PostProcess = item.PostProcess;
    returnItem.ReturnedQuantity = item.AvailableForReturn; // Default to max available

    this.returnStockModel.ReturnedItems.push(returnItem);
  }

  removeReturnItem(index: number) {
    this.returnStockModel.ReturnedItems.splice(index, 1);
  }

  proceedToReturnDetails() {
    if (this.returnStockModel.ReturnedItems.length === 0) {
      this.alertService.error('Please select at least one item to return');
      return;
    }

    // Validate quantities
    for (let item of this.returnStockModel.ReturnedItems) {
      if (item.ReturnedQuantity <= 0) {
        this.alertService.error(`Please enter valid quantity for ${item.ProductName}`);
        return;
      }
    }

    this.currentStep = 3;
  }

  submitReturnStock() {
    if (!this.validateReturnStock()) {
      return;
    }

    this.isLoading = true;
    let url = this.ApiUrl + 'stock/AddReturnStock';
    this.http.post<any>(url, this.returnStockModel).subscribe({
      next: (res) => {
        this.alertService.success('Return stock created successfully. Inspection required.');
        this.resetForm();
        this.loadReturnStockList();
        this.isLoading = false;
      },
      error: (res) => {
        this.alertService.error('Error creating return stock');
        this.isLoading = false;
      }
    });
  }

  validateReturnStock(): boolean {
    if (!this.returnStockModel.ReturnReason) {
      this.alertService.error('Please enter return reason');
      return false;
    }

    if (!this.returnStockModel.ReturnedBy) {
      this.alertService.error('Please enter returned by');
      return false;
    }

    for (let item of this.returnStockModel.ReturnedItems) {
      if (!item.ReturnCondition) {
        this.alertService.error(`Please select condition for ${item.ProductName}`);
        return false;
      }
    }

    return true;
  }

  resetForm() {
    this.returnStockModel = new ReturnStockModel();
    this.dispatchedItems = new DispatchedItemsForReturnModel();
    this.saleOrderNumber = '';
    this.currentStep = 1;
    this.showReturnForm = false;
  }

  showNewReturnForm() {
    this.showReturnForm = true;
    this.resetForm();
  }

  cancelReturn() {
    this.showReturnForm = false;
    this.resetForm();
  }

  applyFilter() {
    this.loadReturnStockList();
  }

  clearFilter() {
    this.filterModel = new ReturnStockFilterModel();
    this.loadReturnStockList();
  }

  viewReturnStock(stockId: number) {
    this.router.navigate(['/inventory/stock-details', stockId]);
  }

  editReturnStock(stockId: number) {
    this.router.navigate(['/inventory/stock-inspection', stockId]);
  }
}
